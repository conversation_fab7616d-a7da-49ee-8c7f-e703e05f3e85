# backend/tts/coqui_engine.py
import subprocess
import tempfile
import os

class CoquiEngine:
    def __init__(self):
        # Use espeak as a fallback TTS engine for compatibility
        self.use_espeak = True
        self.tts = None

        # For now, just use espeak due to TTS library compatibility issues
        print("Using espeak for text-to-speech synthesis")
        try:
            subprocess.run(["espeak", "--version"], capture_output=True, check=True)
        except (subprocess.CalledProcessError, FileNotFoundError):
            raise RuntimeError("espeak is not available for text-to-speech")

    def synthesize_wav(self, text: str, out_wav_path: str) -> None:
        # Use espeak for synthesis
        cmd = [
            "espeak",
            "-s", "150",  # speed
            "-v", "en",   # voice
            "-w", out_wav_path,  # output wav file
            text
        ]
        subprocess.run(cmd, check=True)
