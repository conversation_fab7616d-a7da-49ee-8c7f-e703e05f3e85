import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Alert,
  Dimensions,
  ActivityIndicator,
  StatusBar,
} from 'react-native';
import { CameraView, CameraType, useCameraPermissions } from 'expo-camera';
import { Audio } from 'expo-av';
import CONFIG from '../config';
import lipreadingAPI, { PredictionResult } from '../services/api';
import Playback from './Playback';

const { width, height } = Dimensions.get('window');

const SRAVILipReader: React.FC = () => {
  const [facing, setFacing] = useState<CameraType>('front');
  const [permission, requestPermission] = useCameraPermissions();
  const [audioPermission, setAudioPermission] = useState<boolean>(false);
  const [isRecording, setIsRecording] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [isCreatingSpeakingVideo, setIsCreatingSpeakingVideo] = useState(false);
  const [lastPrediction, setLastPrediction] = useState<string>('');
  const [confidence, setConfidence] = useState<number>(0);
  const [countdown, setCountdown] = useState<number | null>(null);
  const [connectionStatus, setConnectionStatus] = useState<string>('Connecting...');
  
  // Playback state
  const [showPlayback, setShowPlayback] = useState(false);
  const [lastVideoUri, setLastVideoUri] = useState<string>('');
  const [lastPredictionResult, setLastPredictionResult] = useState<PredictionResult | null>(null);
  const [speakingVideoUri, setSpeakingVideoUri] = useState<string>('');

  const cameraRef = useRef<CameraView>(null);

  useEffect(() => {
    requestAudioPermission();
    testServerConnection();
  }, []);

  const testServerConnection = async () => {
    try {
      const isConnected = await lipreadingAPI.testConnection();
      setConnectionStatus(isConnected ? 'Ready' : 'Server offline');
    } catch (error) {
      setConnectionStatus('Connection failed');
    }
  };

  const requestAudioPermission = async () => {
    try {
      // For video recording with audio, camera permission includes microphone
      // Audio playback doesn't require special permissions
      setAudioPermission(true);
      console.log('Audio permission set for video recording and playback');
    } catch (error) {
      console.error('Error setting audio permission:', error);
      setAudioPermission(false);
    }
  };

  const startRecording = async () => {
    if (!cameraRef.current) {
      Alert.alert('Camera Error', 'Camera not available');
      return;
    }

    if (!permission?.granted) {
      Alert.alert('Permission Required', 'Camera permission is required');
      return;
    }

    // Audio permission is handled by camera permission for video recording
    console.log('Starting video recording with camera permission');

    try {
      setIsRecording(true);
      setCountdown(3);

      // Start countdown
      const countdownInterval = setInterval(() => {
        setCountdown((prev) => {
          if (prev === 1) {
            clearInterval(countdownInterval);
            recordVideo();
            return null;
          }
          return prev ? prev - 1 : null;
        });
      }, 1000);

    } catch (error) {
      console.error('Error starting recording:', error);
      setIsRecording(false);
      Alert.alert('Error', 'Failed to start recording');
    }
  };

  const recordVideo = async () => {
    if (!cameraRef.current) return;

    try {
      const video = await cameraRef.current.recordAsync({
        maxDuration: 3,
        quality: '720p',
      });

      setIsRecording(false);

      if (video?.uri) {
        await processVideo(video.uri);
      }
    } catch (error) {
      console.error('Error recording video:', error);
      setIsRecording(false);
      Alert.alert('Error', 'Failed to record video');
    }
  };

  const processVideo = async (videoUri: string) => {
    setIsProcessing(true);

    try {
      const result = await lipreadingAPI.predict(videoUri, 'ICU');
      setLastPrediction(lipreadingAPI.formatPrediction(result));
      setConfidence(result.confidence || 0);
      setLastVideoUri(videoUri);
      setLastPredictionResult(result);

      // Auto-create speaking video
      await createSpeakingVideo(videoUri, result);

    } catch (error) {
      console.error('Error processing video:', error);
      setLastPrediction(`Error: ${error instanceof Error ? error.message : 'Unknown error'}`);
      setConfidence(0);
    } finally {
      setIsProcessing(false);
    }
  };

  const createSpeakingVideo = async (videoUri: string, result: PredictionResult) => {
    setIsCreatingSpeakingVideo(true);

    try {
      const speakingResult = await lipreadingAPI.createSpeakingVideo(
        videoUri,
        result.prediction,
        130
      );

      if (speakingResult.success) {
        setSpeakingVideoUri(speakingResult.videoUri);
        setShowPlayback(true);
      }

    } catch (error) {
      console.error('Error creating speaking video:', error);
      Alert.alert('Speaking Video Error', 'Failed to create speaking video');
    } finally {
      setIsCreatingSpeakingVideo(false);
    }
  };

  const handleClosePlayback = () => {
    setShowPlayback(false);
  };

  if (!permission) {
    return (
      <View style={styles.container}>
        <Text style={styles.loadingText}>Loading camera...</Text>
      </View>
    );
  }

  if (!permission.granted) {
    return (
      <View style={styles.container}>
        <Text style={styles.message}>Camera permission required</Text>
        <TouchableOpacity style={styles.permissionButton} onPress={requestPermission}>
          <Text style={styles.permissionButtonText}>Grant Permission</Text>
        </TouchableOpacity>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="#000" />
      
      {/* Full Screen Camera */}
      <View style={styles.cameraContainer}>
        <CameraView
          ref={cameraRef}
          style={styles.camera}
          facing={facing}
          mode="video"
        />
        
        {/* Simple Oval Guide */}
        <View style={styles.ovalGuide}>
          <View style={styles.oval} />
        </View>

        {/* Countdown */}
        {countdown !== null && (
          <View style={styles.countdownContainer}>
            <Text style={styles.countdownText}>{countdown}</Text>
          </View>
        )}

        {/* Processing Indicator */}
        {(isProcessing || isCreatingSpeakingVideo) && (
          <View style={styles.processingContainer}>
            <ActivityIndicator size="large" color="#fff" />
            <Text style={styles.processingText}>
              {isProcessing ? 'Reading lips...' : 'Creating speaking video...'}
            </Text>
          </View>
        )}
      </View>

      {/* Simple Bottom Section */}
      <View style={styles.bottomSection}>
        {/* Status */}
        <Text style={styles.statusText}>{connectionStatus}</Text>
        
        {/* Prediction Result */}
        {lastPrediction && (
          <View style={styles.resultContainer}>
            <Text style={styles.resultText}>{lastPrediction}</Text>
            <Text style={styles.confidenceText}>
              {(confidence * 100).toFixed(0)}% confidence
            </Text>
          </View>
        )}

        {/* Single Start Button */}
        <TouchableOpacity
          style={[styles.startButton, (isRecording || isProcessing || isCreatingSpeakingVideo) && styles.startButtonDisabled]}
          onPress={startRecording}
          disabled={isRecording || isProcessing || isCreatingSpeakingVideo}
        >
          <Text style={styles.startButtonText}>
            {isRecording ? 'Recording...' : 'Start'}
          </Text>
        </TouchableOpacity>
      </View>

      {/* Speaking Video Playback */}
      {showPlayback && speakingVideoUri && lastPredictionResult && (
        <Playback
          videoUri={speakingVideoUri}
          prediction={lipreadingAPI.formatPrediction(lastPredictionResult)}
          confidence={lastPredictionResult.confidence || 0}
          onClose={handleClosePlayback}
          isSpeakingVideo={true}
        />
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000',
  },
  cameraContainer: {
    flex: 1,
    position: 'relative',
  },
  camera: {
    flex: 1,
  },
  ovalGuide: {
    position: 'absolute',
    top: '50%',
    left: '50%',
    transform: [{ translateX: -150 }, { translateY: -100 }],
    alignItems: 'center',
  },
  oval: {
    width: 300,
    height: 200,
    borderRadius: 150,
    borderWidth: 3,
    borderColor: 'rgba(255, 255, 255, 0.8)',
  },
  countdownContainer: {
    position: 'absolute',
    top: '50%',
    left: '50%',
    transform: [{ translateX: -40 }, { translateY: -40 }],
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: 'rgba(0, 0, 0, 0.8)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  countdownText: {
    color: '#fff',
    fontSize: 32,
    fontWeight: 'bold',
  },
  processingContainer: {
    position: 'absolute',
    top: '50%',
    left: '50%',
    transform: [{ translateX: -80 }, { translateY: -40 }],
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.8)',
    padding: 20,
    borderRadius: 10,
  },
  processingText: {
    color: '#fff',
    fontSize: 16,
    marginTop: 10,
  },
  bottomSection: {
    backgroundColor: '#000',
    padding: 30,
    alignItems: 'center',
  },
  statusText: {
    color: '#888',
    fontSize: 14,
    marginBottom: 10,
  },
  resultContainer: {
    alignItems: 'center',
    marginBottom: 20,
  },
  resultText: {
    color: '#fff',
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  confidenceText: {
    color: '#888',
    fontSize: 16,
    marginTop: 5,
  },
  startButton: {
    backgroundColor: '#007AFF',
    paddingHorizontal: 60,
    paddingVertical: 20,
    borderRadius: 30,
  },
  startButtonDisabled: {
    backgroundColor: '#555',
  },
  startButtonText: {
    color: '#fff',
    fontSize: 20,
    fontWeight: 'bold',
  },
  loadingText: {
    color: '#fff',
    fontSize: 18,
    textAlign: 'center',
    marginTop: 100,
  },
  message: {
    color: '#fff',
    fontSize: 18,
    textAlign: 'center',
    marginTop: 100,
    marginBottom: 30,
  },
  permissionButton: {
    backgroundColor: '#007AFF',
    paddingHorizontal: 30,
    paddingVertical: 15,
    borderRadius: 10,
    alignSelf: 'center',
  },
  permissionButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
});

export default SRAVILipReader;
