import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Dimensions,
  Alert,
  ActivityIndicator,
  Animated,
} from 'react-native';
import { VideoView, useVideoPlayer } from 'expo-video';
import { Audio } from 'expo-av';
import * as Speech from 'expo-speech';

const { width, height } = Dimensions.get('window');

interface PlaybackProps {
  videoUri: string;
  prediction: string;
  confidence: number;
  onClose: () => void;
  onReplay?: () => void;
  ttsDelay?: number; // Configurable TTS delay in milliseconds
  ttsRateMultiplier?: number; // Multiplier for TTS rate calculation
  isSpeakingVideo?: boolean; // Whether this is a server-generated speaking video
}

const Playback: React.FC<PlaybackProps> = ({
  videoUri,
  prediction,
  confidence,
  onClose,
  onReplay,
  ttsDelay = 130, // Default 130ms delay for better lip-sync
  ttsRateMultiplier = 1.0, // Default rate multiplier
  isSpeakingVideo = false, // Default to client-side TTS
}) => {
  const [isVideoLoaded, setIsVideoLoaded] = useState(false);
  const [isPlaying, setIsPlaying] = useState(false);
  const [hasPlayedTTS, setHasPlayedTTS] = useState(false);
  const [videoError, setVideoError] = useState<string | null>(null);
  const [showCaptions, setShowCaptions] = useState(false);
  const [videoDuration, setVideoDuration] = useState<number>(8000); // Default 8 seconds
  const [currentPosition, setCurrentPosition] = useState<number>(0);

  // Video player with expo-video
  const player = useVideoPlayer(videoUri, (player) => {
    player.loop = false;
    player.muted = false;
  });

  const captionFadeAnim = useRef(new Animated.Value(0)).current;
  const ttsTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    // Initialize audio session for video playback
    const initializeAudio = async () => {
      try {
        // Request audio permissions first
        const { status } = await Audio.requestPermissionsAsync();
        if (status !== 'granted') {
          console.warn('Audio permissions not granted');
          return;
        }

        // Set audio mode for video playback
        await Audio.setAudioModeAsync({
          allowsRecordingIOS: false,
          staysActiveInBackground: false,
          playsInSilentModeIOS: true,
          shouldDuckAndroid: true,
          playThroughEarpieceAndroid: false,
          interruptionModeIOS: Audio.INTERRUPTION_MODE_IOS_DO_NOT_MIX,
          interruptionModeAndroid: Audio.INTERRUPTION_MODE_ANDROID_DO_NOT_MIX,
        });
        console.log('Audio session initialized successfully');
      } catch (error) {
        console.error('Failed to initialize audio session:', error);
      }
    };

    initializeAudio();

    // Set up video player event listeners
    const subscription = player.addListener('playingChange', (isPlaying) => {
      setIsPlaying(isPlaying);
      console.log('Video playing state changed:', isPlaying);
    });

    const statusSubscription = player.addListener('statusChange', (status) => {
      if (status === 'readyToPlay') {
        setIsVideoLoaded(true);
        setVideoDuration(player.duration * 1000); // Convert to milliseconds
        console.log('Video loaded, duration:', player.duration);
      } else if (status === 'error') {
        setVideoError('Video playback error');
        console.error('Video player error');
      }
    });

    // Cleanup TTS, timeouts, and subscriptions when component unmounts
    return () => {
      Speech.stop();
      if (ttsTimeoutRef.current) {
        clearTimeout(ttsTimeoutRef.current);
      }
      subscription?.remove();
      statusSubscription?.remove();
    };
  }, [player]);

  const handleVideoLoad = (status: AVPlaybackStatus) => {
    if (status.isLoaded) {
      setIsVideoLoaded(true);
      setVideoError(null);

      // Extract video duration for better TTS synchronization
      if (status.durationMillis) {
        setVideoDuration(status.durationMillis);
        console.log(`Video loaded successfully - Duration: ${status.durationMillis}ms`);
      } else {
        console.log('Video loaded successfully');
      }
    }
  };

  const handleVideoError = (error: string) => {
    console.error('Video playback error:', error);
    setVideoError(error);
    Alert.alert(
      'Video Error',
      'Unable to play the recorded video. Please try recording again.',
      [{ text: 'OK', onPress: onClose }]
    );
  };

  const handlePlaybackStatusUpdate = (status: AVPlaybackStatus) => {
    if (status.isLoaded) {
      setIsPlaying(status.isPlaying);

      // Update current position for better synchronization tracking
      if (status.positionMillis !== undefined) {
        setCurrentPosition(status.positionMillis);
      }

      // Start TTS with configurable delay when video starts playing (only for client-side TTS)
      if (!isSpeakingVideo && status.isPlaying && !hasPlayedTTS && status.positionMillis !== undefined) {
        if (status.positionMillis >= ttsDelay) {
          startSynchronizedTTS();
        } else if (status.positionMillis === 0) {
          // Schedule TTS to start after the configured delay
          scheduleTTSStart();
        }
      }

      // Show captions when video starts
      if (status.isPlaying && !showCaptions) {
        setShowCaptions(true);
        Animated.timing(captionFadeAnim, {
          toValue: 1,
          duration: 300,
          useNativeDriver: true,
        }).start();
      }
    }
  };

  const scheduleTTSStart = () => {
    // Clear any existing timeout
    if (ttsTimeoutRef.current) {
      clearTimeout(ttsTimeoutRef.current);
    }

    // Schedule TTS to start after the configured delay
    ttsTimeoutRef.current = setTimeout(() => {
      startSynchronizedTTS();
    }, ttsDelay);

    console.log(`TTS scheduled to start in ${ttsDelay}ms`);
  };

  const calculateOptimalTTSRate = (): number => {
    const videoDurationSeconds = videoDuration / 1000;
    const phraseWords = prediction.split(' ').length;
    const syllableCount = prediction.split(' ').reduce((count, word) => {
      // Rough syllable estimation: vowel groups
      const syllables = word.toLowerCase().match(/[aeiouy]+/g)?.length || 1;
      return count + syllables;
    }, 0);

    // Base rate calculation considering video duration and content
    // Average speaking rate is ~150 words per minute or ~2.5 words per second
    // For lip-sync, we want to match the video duration more closely
    const targetWordsPerSecond = phraseWords / (videoDurationSeconds * 0.8); // Use 80% of video duration
    const normalWordsPerSecond = 2.5;
    const calculatedRate = Math.max(0.3, Math.min(1.2, targetWordsPerSecond / normalWordsPerSecond));

    // Apply user-defined multiplier
    const finalRate = calculatedRate * ttsRateMultiplier;

    console.log(`TTS Rate Calculation:
      - Video Duration: ${videoDurationSeconds.toFixed(1)}s
      - Phrase Words: ${phraseWords}
      - Syllables: ${syllableCount}
      - Target Rate: ${calculatedRate.toFixed(2)}
      - Final Rate (with multiplier ${ttsRateMultiplier}): ${finalRate.toFixed(2)}`);

    return Math.max(0.3, Math.min(1.5, finalRate));
  };

  const startSynchronizedTTS = async () => {
    if (hasPlayedTTS || !prediction.trim()) return;

    setHasPlayedTTS(true);

    try {
      const optimalRate = calculateOptimalTTSRate();

      console.log(`Starting TTS: "${prediction}" with optimized rate: ${optimalRate.toFixed(2)}`);

      await Speech.speak(prediction, {
        rate: optimalRate,
        pitch: 1.0,
        language: 'en-US',
        quality: Speech.VoiceQuality.Enhanced,
        onStart: () => console.log('TTS started'),
        onDone: () => console.log('TTS completed'),
        onError: (error) => console.error('TTS error:', error),
      });
    } catch (error) {
      console.error('Error starting TTS:', error);
    }
  };

  const handlePlayVideo = async () => {
    if (!player || !isVideoLoaded) return;

    try {
      // Ensure audio permissions and session are active before playing
      const { status } = await Audio.requestPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert('Audio Permission Required', 'Please enable audio permissions to hear the speaking video');
        return;
      }

      await Audio.setAudioModeAsync({
        allowsRecordingIOS: false,
        staysActiveInBackground: false,
        playsInSilentModeIOS: true,
        shouldDuckAndroid: true,
        playThroughEarpieceAndroid: false,
        interruptionModeIOS: Audio.INTERRUPTION_MODE_IOS_DO_NOT_MIX,
        interruptionModeAndroid: Audio.INTERRUPTION_MODE_ANDROID_DO_NOT_MIX,
      });

      // Reset TTS state for replay
      setHasPlayedTTS(false);
      setShowCaptions(false);
      setCurrentPosition(0);
      captionFadeAnim.setValue(0);

      // Stop any existing TTS and clear timeouts
      Speech.stop();
      if (ttsTimeoutRef.current) {
        clearTimeout(ttsTimeoutRef.current);
        ttsTimeoutRef.current = null;
      }

      // Start video from beginning with expo-video
      player.currentTime = 0;
      player.play();

      console.log('Video playback started with audio session activated');
    } catch (error) {
      console.error('Error playing video:', error);
      Alert.alert('Playback Error', `Unable to play video: ${error.message || 'Unknown error'}`);
    }
  };

  const handleStopVideo = async () => {
    if (!player) return;

    try {
      player.pause();
      player.currentTime = 0;
      Speech.stop();

      // Clear any scheduled TTS
      if (ttsTimeoutRef.current) {
        clearTimeout(ttsTimeoutRef.current);
        ttsTimeoutRef.current = null;
      }

      setIsPlaying(false);
      setCurrentPosition(0);
      setHasPlayedTTS(false);
      setShowCaptions(false);
      captionFadeAnim.setValue(0);
      console.log('Video playback stopped');
    } catch (error) {
      console.error('Error stopping video:', error);
    }
  };

  const handleReplay = () => {
    handlePlayVideo();
    onReplay?.();
  };

  const getConfidenceColor = (conf: number): string => {
    if (conf >= 0.8) return '#4CAF50'; // Green
    if (conf >= 0.6) return '#FF9800'; // Orange
    return '#F44336'; // Red
  };

  const getConfidenceText = (conf: number): string => {
    if (conf >= 0.8) return 'High Confidence';
    if (conf >= 0.6) return 'Medium Confidence';
    return 'Low Confidence';
  };

  return (
    <View style={styles.overlay}>
      <View style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <Text style={styles.headerTitle}>
          {isSpeakingVideo ? '🎬 Speaking Video' : '📹 Video Playback'}
        </Text>
        <TouchableOpacity style={styles.closeButton} onPress={onClose}>
          <Text style={styles.closeButtonText}>✕</Text>
        </TouchableOpacity>
      </View>

      {/* Video Player */}
      <View style={styles.videoContainer}>
        {videoError ? (
          <View style={styles.errorContainer}>
            <Text style={styles.errorText}>Video Error</Text>
            <Text style={styles.errorSubtext}>{videoError}</Text>
          </View>
        ) : (
          <VideoView
            player={player}
            style={styles.video}
            allowsFullscreen={false}
            allowsPictureInPicture={false}
          />
        )}
        
        {/* Loading Overlay */}
        {!isVideoLoaded && !videoError && (
          <View style={styles.loadingOverlay}>
            <ActivityIndicator size="large" color="#fff" />
            <Text style={styles.loadingText}>Loading video...</Text>
          </View>
        )}
        
        {/* Caption Overlay */}
        {showCaptions && (
          <Animated.View 
            style={[
              styles.captionOverlay,
              { opacity: captionFadeAnim }
            ]}
          >
            <Text style={styles.captionText}>{prediction}</Text>
          </Animated.View>
        )}
      </View>

      {/* Prediction Info */}
      <View style={styles.infoContainer}>
        <Text style={styles.predictionLabel}>Predicted Phrase:</Text>
        <Text style={styles.predictionText}>{prediction}</Text>
        
        <View style={styles.confidenceContainer}>
          <Text style={styles.confidenceLabel}>
            {getConfidenceText(confidence)}
          </Text>
          <Text style={[
            styles.confidenceValue,
            { color: getConfidenceColor(confidence) }
          ]}>
            {(confidence * 100).toFixed(1)}%
          </Text>
        </View>

        {/* Synchronization Info */}
        <View style={styles.syncInfoContainer}>
          <Text style={styles.syncInfoLabel}>
            {isSpeakingVideo ? 'Server-Side Speaking Video:' : 'Client-Side TTS Settings:'}
          </Text>
          {isSpeakingVideo ? (
            <>
              <Text style={styles.syncInfoText}>
                ✅ Audio pre-synchronized with 130ms delay
              </Text>
              <Text style={styles.syncInfoText}>
                🎵 Server-generated TTS with speed matching
              </Text>
              <Text style={styles.syncInfoText}>
                Duration: {(videoDuration / 1000).toFixed(1)}s | Words: {prediction.split(' ').length}
              </Text>
            </>
          ) : (
            <>
              <Text style={styles.syncInfoText}>
                TTS Delay: {ttsDelay}ms | Duration: {(videoDuration / 1000).toFixed(1)}s
              </Text>
              <Text style={styles.syncInfoText}>
                Words: {prediction.split(' ').length} | Rate Multiplier: {ttsRateMultiplier}x
              </Text>
            </>
          )}
        </View>
      </View>

      {/* Control Buttons */}
      <View style={styles.controlsContainer}>
        <TouchableOpacity
          style={[styles.button, styles.playButton]}
          onPress={isPlaying ? handleStopVideo : handlePlayVideo}
          disabled={!isVideoLoaded}
        >
          <Text style={styles.buttonText}>
            {isPlaying ? '⏸️ Pause' : (isSpeakingVideo ? '🎬 Play Speaking Video' : '▶️ Play Video')}
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.button, styles.replayButton]}
          onPress={handleReplay}
          disabled={!isVideoLoaded}
        >
          <Text style={styles.buttonText}>🔄 Replay</Text>
        </TouchableOpacity>
      </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  overlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.95)',
    zIndex: 1000,
  },
  container: {
    flex: 1,
    backgroundColor: 'transparent',
    justifyContent: 'flex-start',
    alignItems: 'center',
    paddingTop: 50, // Account for status bar
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    width: '100%',
    paddingHorizontal: 20,
    paddingVertical: 15,
    marginBottom: 20,
  },
  headerTitle: {
    color: '#fff',
    fontSize: 20,
    fontWeight: 'bold',
  },
  closeButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  closeButtonText: {
    color: '#fff',
    fontSize: 18,
    fontWeight: 'bold',
  },
  videoContainer: {
    width: width * 0.9,
    height: height * 0.5,
    borderRadius: 12,
    overflow: 'hidden',
    position: 'relative',
    backgroundColor: '#1a1a1a',
  },
  video: {
    width: '100%',
    height: '100%',
  },
  loadingOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    color: '#fff',
    fontSize: 16,
    marginTop: 10,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorText: {
    color: '#F44336',
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  errorSubtext: {
    color: '#fff',
    fontSize: 14,
    textAlign: 'center',
  },
  captionOverlay: {
    position: 'absolute',
    bottom: 20,
    left: 20,
    right: 20,
    backgroundColor: 'rgba(0, 0, 0, 0.8)',
    borderRadius: 8,
    padding: 12,
  },
  captionText: {
    color: '#fff',
    fontSize: 18,
    fontWeight: 'bold',
    textAlign: 'center',
    textShadowColor: 'rgba(0, 0, 0, 0.8)',
    textShadowOffset: { width: 1, height: 1 },
    textShadowRadius: 2,
  },
  infoContainer: {
    width: width * 0.9,
    marginTop: 20,
    padding: 16,
    backgroundColor: '#1a1a1a',
    borderRadius: 12,
  },
  predictionLabel: {
    color: '#888',
    fontSize: 14,
    marginBottom: 4,
  },
  predictionText: {
    color: '#fff',
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 12,
  },
  confidenceContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  confidenceLabel: {
    color: '#888',
    fontSize: 14,
  },
  confidenceValue: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  syncInfoContainer: {
    marginTop: 12,
    padding: 8,
    backgroundColor: '#2a2a2a',
    borderRadius: 6,
  },
  syncInfoLabel: {
    color: '#888',
    fontSize: 12,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  syncInfoText: {
    color: '#ccc',
    fontSize: 11,
    marginBottom: 2,
  },
  controlsContainer: {
    width: width * 0.9,
    marginTop: 20,
    gap: 12,
  },
  button: {
    paddingVertical: 14,
    paddingHorizontal: 20,
    borderRadius: 8,
    alignItems: 'center',
  },
  playButton: {
    backgroundColor: '#4CAF50',
  },
  replayButton: {
    backgroundColor: '#2196F3',
  },
  buttonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
});

export default Playback;
