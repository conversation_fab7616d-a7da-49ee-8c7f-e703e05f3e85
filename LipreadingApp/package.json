{"name": "<PERSON><PERSON><PERSON><PERSON>", "version": "1.0.0", "main": "index.ts", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web"}, "dependencies": {"@react-native-community/slider": "^5.0.1", "expo": "~53.0.22", "expo-av": "~15.1.7", "expo-camera": "~16.1.11", "expo-speech": "~13.1.7", "expo-status-bar": "~2.2.3", "react": "19.0.0", "react-native": "0.79.6", "expo-video": "~2.2.2"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/react": "~19.0.10", "typescript": "~5.8.3"}, "private": true}